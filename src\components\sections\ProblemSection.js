'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { AlertTriangle, Clock, HelpCircle, Users, TrendingDown, FileX } from 'lucide-react';
import Card, { FeatureCard, StatCard } from '../ui/Card';

export default function ProblemSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 50 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const problems = [
    {
      icon: <HelpCircle className="w-8 h-8" />,
      title: "Complex Reporting Processes",
      description: "Citizens face confusion navigating multiple platforms and departments when reporting offenses like traffic violations, counterfeit goods, or public disturbances.",
      stats: "78% of people don't report due to complexity",
      color: "text-red-400"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "Lack of Immediate Action",
      description: "People witness offenses but avoid reporting due to time-consuming procedures and uncertainty about which authority to contact.",
      stats: "Average reporting time: 45+ minutes",
      color: "text-orange-400"
    },
    {
      icon: <FileX className="w-8 h-8" />,
      title: "No Unified Solution",
      description: "There's a critical demand for a streamlined platform that allows easy submission of reports with multimedia evidence and real-time status updates.",
      stats: "89% want a simple mobile solution",
      color: "text-yellow-400"
    }
  ];

  const painPoints = [
    {
      icon: <AlertTriangle className="w-6 h-6" />,
      title: "Language Barriers",
      description: "Multi-language support needed for diverse populations",
      impact: "65% struggle with language"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Authority Confusion",
      description: "Users don't know which department to contact",
      impact: "72% contact wrong authority"
    },
    {
      icon: <TrendingDown className="w-6 h-6" />,
      title: "No Follow-up",
      description: "Zero visibility into report status and progress",
      impact: "91% never get updates"
    }
  ];

  const currentStats = [
    { value: "2.3M", label: "Unreported Incidents", trend: "+15%", trendDirection: "up" },
    { value: "45min", label: "Average Report Time", trend: "+8%", trendDirection: "up" },
    { value: "23%", label: "Successful Reports", trend: "-12%", trendDirection: "down" }
  ];

  return (
    <section ref={ref} className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-red-900/5 to-transparent" />
      
      {/* Floating Elements */}
      <motion.div
        className="absolute top-10 right-10 w-32 h-32 bg-red-500/5 rounded-full blur-xl"
        animate={{ 
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{ duration: 4, repeat: Infinity }}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate={isInView ? "animate" : "initial"}
          className="space-y-16"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center max-w-4xl mx-auto">
            <motion.div
              className="inline-flex items-center space-x-2 bg-red-500/10 border border-red-500/20 rounded-full px-4 py-2 mb-6"
              whileHover={{ scale: 1.05 }}
            >
              <AlertTriangle className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">The Problem</span>
            </motion.div>
            
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent">
                Reporting is Broken
              </span>
            </h2>
            
            <p className="text-xl text-gray-300 leading-relaxed">
              Current offense reporting systems across Malaysia and Singapore are fragmented, 
              time-consuming, and frustrating for citizens who want to make a difference.
            </p>
          </motion.div>

          {/* Current Statistics */}
          <motion.div variants={itemVariants}>
            <h3 className="text-2xl font-bold text-center mb-8 text-white">
              Current State of Reporting
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {currentStats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                  transition={{ delay: 0.2 * index, duration: 0.6 }}
                >
                  <StatCard
                    value={stat.value}
                    label={stat.label}
                    trend={stat.trend}
                    trendDirection={stat.trendDirection}
                    className="border-red-500/20 bg-red-900/10"
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Main Problems */}
          <motion.div variants={itemVariants}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {problems.map((problem, index) => (
                <motion.div
                  key={problem.title}
                  initial={{ opacity: 0, y: 50 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                  transition={{ delay: 0.3 * index, duration: 0.8 }}
                >
                  <FeatureCard
                    icon={problem.icon}
                    title={problem.title}
                    description={problem.description}
                    iconColor={problem.color}
                    className="h-full border-red-500/20 hover:border-red-400/40"
                  >
                    <div className="mt-4 pt-4 border-t border-red-500/20">
                      <div className="text-sm text-red-400 font-semibold">
                        {problem.stats}
                      </div>
                    </div>
                  </FeatureCard>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Pain Points Grid */}
          <motion.div variants={itemVariants}>
            <h3 className="text-2xl font-bold text-center mb-8 text-white">
              Key Pain Points
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {painPoints.map((point, index) => (
                <motion.div
                  key={point.title}
                  initial={{ opacity: 0, x: -30 }}
                  animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
                  transition={{ delay: 0.2 * index, duration: 0.6 }}
                >
                  <Card className="p-6 border-orange-500/20 bg-orange-900/10 hover:bg-orange-900/20 transition-colors">
                    <div className="flex items-start space-x-4">
                      <div className="text-orange-400 mt-1">
                        {point.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-white mb-2">
                          {point.title}
                        </h4>
                        <p className="text-gray-300 text-sm mb-3">
                          {point.description}
                        </p>
                        <div className="text-xs text-orange-400 font-medium">
                          {point.impact}
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div 
            variants={itemVariants}
            className="text-center bg-gradient-to-r from-red-900/20 to-orange-900/20 rounded-2xl p-8 border border-red-500/20"
          >
            <h3 className="text-2xl font-bold text-white mb-4">
              It's Time for Change
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Citizens deserve a better way to report offenses and contribute to safer communities. 
              The current system is failing everyone involved.
            </p>
            <motion.div
              className="text-4xl font-bold bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent"
              animate={{ 
                scale: [1, 1.05, 1],
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              We Have the Solution
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
