{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/ui/Logo.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport default function Logo({ size = 'md', animated = true, showText = true }) {\n  const sizes = {\n    sm: { width: 32, height: 32, text: 'text-lg' },\n    md: { width: 48, height: 48, text: 'text-xl' },\n    lg: { width: 64, height: 64, text: 'text-2xl' },\n    xl: { width: 80, height: 80, text: 'text-3xl' },\n  };\n\n  const currentSize = sizes[size];\n\n  const logoVariants = {\n    initial: { scale: 0, rotate: -180 },\n    animate: { \n      scale: 1, \n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 260,\n        damping: 20,\n        duration: 1.2\n      }\n    },\n    hover: {\n      scale: 1.1,\n      rotate: 5,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  const textVariants = {\n    initial: { opacity: 0, x: -20 },\n    animate: { \n      opacity: 1, \n      x: 0,\n      transition: { delay: 0.5, duration: 0.8 }\n    }\n  };\n\n  const pathVariants = {\n    initial: { pathLength: 0, opacity: 0 },\n    animate: { \n      pathLength: 1, \n      opacity: 1,\n      transition: { duration: 2, ease: \"easeInOut\" }\n    }\n  };\n\n  return (\n    <div className=\"flex items-center space-x-3\">\n      <motion.div\n        variants={animated ? logoVariants : {}}\n        initial={animated ? \"initial\" : \"animate\"}\n        animate=\"animate\"\n        whileHover={animated ? \"hover\" : {}}\n        className=\"relative\"\n      >\n        <svg\n          width={currentSize.width}\n          height={currentSize.height}\n          viewBox=\"0 0 100 100\"\n          className=\"drop-shadow-lg\"\n        >\n          {/* Background Circle with Gradient */}\n          <defs>\n            <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#0ea5e9\" />\n              <stop offset=\"50%\" stopColor=\"#14b8a6\" />\n              <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n            </linearGradient>\n            <filter id=\"glow\">\n              <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n              <feMerge> \n                <feMergeNode in=\"coloredBlur\"/>\n                <feMergeNode in=\"SourceGraphic\"/>\n              </feMerge>\n            </filter>\n          </defs>\n          \n          {/* Main Circle */}\n          <motion.circle\n            cx=\"50\"\n            cy=\"50\"\n            r=\"45\"\n            fill=\"url(#logoGradient)\"\n            filter=\"url(#glow)\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n          \n          {/* Report Icon - Document with Exclamation */}\n          <motion.path\n            d=\"M30 25 L70 25 L70 75 L30 75 Z\"\n            fill=\"white\"\n            stroke=\"none\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n          \n          {/* Exclamation Mark */}\n          <motion.circle\n            cx=\"50\"\n            cy=\"65\"\n            r=\"2\"\n            fill=\"#0ea5e9\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n          <motion.rect\n            x=\"48\"\n            y=\"35\"\n            width=\"4\"\n            height=\"20\"\n            fill=\"#0ea5e9\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n          \n          {/* Connection Lines (representing cross-border) */}\n          <motion.path\n            d=\"M20 50 L30 50 M70 50 L80 50\"\n            stroke=\"white\"\n            strokeWidth=\"3\"\n            strokeLinecap=\"round\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n          \n          {/* Small Dots (representing Malaysia and Singapore) */}\n          <motion.circle\n            cx=\"15\"\n            cy=\"50\"\n            r=\"3\"\n            fill=\"#14b8a6\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n          <motion.circle\n            cx=\"85\"\n            cy=\"50\"\n            r=\"3\"\n            fill=\"#8b5cf6\"\n            variants={animated ? pathVariants : {}}\n            initial={animated ? \"initial\" : \"animate\"}\n            animate=\"animate\"\n          />\n        </svg>\n      </motion.div>\n      \n      {showText && (\n        <motion.div\n          variants={animated ? textVariants : {}}\n          initial={animated ? \"initial\" : \"animate\"}\n          animate=\"animate\"\n          className={`font-bold ${currentSize.text} bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent`}\n        >\n          ReportU\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,KAAK,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAE,WAAW,IAAI,EAAE;IAC5E,MAAM,QAAQ;QACZ,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,MAAM;QAAU;QAC7C,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,MAAM;QAAU;QAC7C,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,MAAM;QAAW;QAC9C,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,MAAM;QAAW;IAChD;IAEA,MAAM,cAAc,KAAK,CAAC,KAAK;IAE/B,MAAM,eAAe;QACnB,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;gBACT,UAAU;YACZ;QACF;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,OAAO;gBAAK,UAAU;YAAI;QAC1C;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,YAAY;YAAG,SAAS;QAAE;QACrC,SAAS;YACP,YAAY;YACZ,SAAS;YACT,YAAY;gBAAE,UAAU;gBAAG,MAAM;YAAY;QAC/C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,WAAW,eAAe,CAAC;gBACrC,SAAS,WAAW,YAAY;gBAChC,SAAQ;gBACR,YAAY,WAAW,UAAU,CAAC;gBAClC,WAAU;0BAEV,cAAA,8OAAC;oBACC,OAAO,YAAY,KAAK;oBACxB,QAAQ,YAAY,MAAM;oBAC1B,SAAQ;oBACR,WAAU;;sCAGV,8OAAC;;8CACC,8OAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAe,cAAa;4CAAI,QAAO;;;;;;sDACxC,8OAAC;;8DACC,8OAAC;oDAAY,IAAG;;;;;;8DAChB,8OAAC;oDAAY,IAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;sCAIV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;sCAIV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,MAAK;4BACL,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;sCAIV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;sCAIV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,UAAU,WAAW,eAAe,CAAC;4BACrC,SAAS,WAAW,YAAY;4BAChC,SAAQ;;;;;;;;;;;;;;;;;YAKb,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,WAAW,eAAe,CAAC;gBACrC,SAAS,WAAW,YAAY;gBAChC,SAAQ;gBACR,WAAW,CAAC,UAAU,EAAE,YAAY,IAAI,CAAC,yEAAyE,CAAC;0BACpH;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({ \n  children, \n  variant = 'primary', \n  size = 'md', \n  className = '', \n  disabled = false,\n  loading = false,\n  icon,\n  iconPosition = 'left',\n  onClick,\n  ...props \n}, ref) => {\n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-teal-500 text-white hover:from-blue-600 hover:to-teal-600 shadow-lg hover:shadow-xl',\n    secondary: 'glass text-white hover:bg-white/20 border border-white/20',\n    outline: 'border-2 border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white',\n    ghost: 'text-blue-400 hover:bg-blue-500/10 hover:text-blue-300',\n    danger: 'bg-gradient-to-r from-red-500 to-pink-500 text-white hover:from-red-600 hover:to-pink-600',\n  };\n\n  const sizes = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg',\n    xl: 'px-10 py-5 text-xl',\n  };\n\n  const baseClasses = `\n    inline-flex items-center justify-center\n    font-semibold rounded-lg\n    transition-all duration-300\n    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-transparent\n    disabled:opacity-50 disabled:cursor-not-allowed\n    relative overflow-hidden\n  `;\n\n  const buttonVariants = {\n    initial: { scale: 1 },\n    hover: { \n      scale: 1.05,\n      transition: { duration: 0.2 }\n    },\n    tap: { \n      scale: 0.95,\n      transition: { duration: 0.1 }\n    }\n  };\n\n  const rippleVariants = {\n    initial: { scale: 0, opacity: 0.5 },\n    animate: { \n      scale: 4, \n      opacity: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const handleClick = (e) => {\n    if (disabled || loading) return;\n    \n    // Create ripple effect\n    const button = e.currentTarget;\n    const rect = button.getBoundingClientRect();\n    const size = Math.max(rect.width, rect.height);\n    const x = e.clientX - rect.left - size / 2;\n    const y = e.clientY - rect.top - size / 2;\n    \n    const ripple = document.createElement('span');\n    ripple.style.cssText = `\n      position: absolute;\n      left: ${x}px;\n      top: ${y}px;\n      width: ${size}px;\n      height: ${size}px;\n      background: rgba(255, 255, 255, 0.3);\n      border-radius: 50%;\n      transform: scale(0);\n      animation: ripple 0.6s linear;\n      pointer-events: none;\n    `;\n    \n    button.appendChild(ripple);\n    setTimeout(() => ripple.remove(), 600);\n    \n    if (onClick) onClick(e);\n  };\n\n  const LoadingSpinner = () => (\n    <svg \n      className=\"animate-spin -ml-1 mr-2 h-4 w-4\" \n      xmlns=\"http://www.w3.org/2000/svg\" \n      fill=\"none\" \n      viewBox=\"0 0 24 24\"\n    >\n      <circle \n        className=\"opacity-25\" \n        cx=\"12\" \n        cy=\"12\" \n        r=\"10\" \n        stroke=\"currentColor\" \n        strokeWidth=\"4\"\n      />\n      <path \n        className=\"opacity-75\" \n        fill=\"currentColor\" \n        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      />\n    </svg>\n  );\n\n  return (\n    <motion.button\n      ref={ref}\n      className={`\n        ${baseClasses}\n        ${variants[variant]}\n        ${sizes[size]}\n        ${className}\n      `}\n      variants={buttonVariants}\n      initial=\"initial\"\n      whileHover={!disabled && !loading ? \"hover\" : \"initial\"}\n      whileTap={!disabled && !loading ? \"tap\" : \"initial\"}\n      disabled={disabled || loading}\n      onClick={handleClick}\n      {...props}\n    >\n      {loading && <LoadingSpinner />}\n      \n      {icon && iconPosition === 'left' && !loading && (\n        <span className=\"mr-2\">{icon}</span>\n      )}\n      \n      <span className={loading ? 'opacity-70' : ''}>\n        {children}\n      </span>\n      \n      {icon && iconPosition === 'right' && !loading && (\n        <span className=\"ml-2\">{icon}</span>\n      )}\n      \n      {/* Shine effect for primary buttons */}\n      {variant === 'primary' && (\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12\"\n          initial={{ x: '-100%' }}\n          whileHover={{ x: '100%' }}\n          transition={{ duration: 0.8 }}\n        />\n      )}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n\n// CSS for ripple animation (add to globals.css)\nconst rippleCSS = `\n@keyframes ripple {\n  to {\n    transform: scale(4);\n    opacity: 0;\n  }\n}\n`;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,OAAO,EACP,GAAG,OACJ,EAAE;IACD,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;;;;;;;EAOrB,CAAC;IAED,MAAM,iBAAiB;QACrB,SAAS;YAAE,OAAO;QAAE;QACpB,OAAO;YACL,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,KAAK;YACH,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB,SAAS;YAAE,OAAO;YAAG,SAAS;QAAI;QAClC,SAAS;YACP,OAAO;YACP,SAAS;YACT,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,YAAY,SAAS;QAEzB,uBAAuB;QACvB,MAAM,SAAS,EAAE,aAAa;QAC9B,MAAM,OAAO,OAAO,qBAAqB;QACzC,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM;QAC7C,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,OAAO;QACzC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,GAAG,OAAO;QAExC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,CAAC,OAAO,GAAG,CAAC;;YAEhB,EAAE,EAAE;WACL,EAAE,EAAE;aACF,EAAE,KAAK;cACN,EAAE,KAAK;;;;;;IAMjB,CAAC;QAED,OAAO,WAAW,CAAC;QACnB,WAAW,IAAM,OAAO,MAAM,IAAI;QAElC,IAAI,SAAS,QAAQ;IACvB;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;IAKR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAC;QACV,EAAE,YAAY;QACd,EAAE,QAAQ,CAAC,QAAQ,CAAC;QACpB,EAAE,KAAK,CAAC,KAAK,CAAC;QACd,EAAE,UAAU;MACd,CAAC;QACD,UAAU;QACV,SAAQ;QACR,YAAY,CAAC,YAAY,CAAC,UAAU,UAAU;QAC9C,UAAU,CAAC,YAAY,CAAC,UAAU,QAAQ;QAC1C,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBAAW,8OAAC;;;;;YAEZ,QAAQ,iBAAiB,UAAU,CAAC,yBACnC,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;0BAG1B,8OAAC;gBAAK,WAAW,UAAU,eAAe;0BACvC;;;;;;YAGF,QAAQ,iBAAiB,WAAW,CAAC,yBACpC,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAIzB,YAAY,2BACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;gBAAQ;gBACtB,YAAY;oBAAE,GAAG;gBAAO;gBACxB,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAKtC;AAEA,OAAO,WAAW,GAAG;uCAEN;AAEf,gDAAgD;AAChD,MAAM,YAAY,CAAC;;;;;;;AAOnB,CAAC", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/layout/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport Logo from '../ui/Logo';\nimport Button from '../ui/Button';\nimport { Menu, X, ChevronDown } from 'lucide-react';\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const pathname = usePathname();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'Features', href: '/#features' },\n    { name: 'Pricing', href: '/#pricing' },\n    { name: 'About', href: '/about' },\n  ];\n\n  const navVariants = {\n    initial: { y: -100, opacity: 0 },\n    animate: { \n      y: 0, \n      opacity: 1,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    }\n  };\n\n  const mobileMenuVariants = {\n    closed: {\n      opacity: 0,\n      height: 0,\n      transition: {\n        duration: 0.3,\n        ease: \"easeInOut\"\n      }\n    },\n    open: {\n      opacity: 1,\n      height: \"auto\",\n      transition: {\n        duration: 0.3,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  const linkVariants = {\n    initial: { opacity: 0, y: -10 },\n    animate: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.3 }\n    },\n    hover: {\n      scale: 1.05,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  return (\n    <motion.nav\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled \n          ? 'glass backdrop-blur-md border-b border-white/10' \n          : 'bg-transparent'\n      }`}\n      variants={navVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <Link href=\"/\" className=\"flex items-center\">\n              <Logo size=\"md\" animated={true} />\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.div\n                key={item.name}\n                variants={linkVariants}\n                initial=\"initial\"\n                animate=\"animate\"\n                whileHover=\"hover\"\n                transition={{ delay: 0.1 * index }}\n              >\n                <Link\n                  href={item.href}\n                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200 ${\n                    pathname === item.href\n                      ? 'text-blue-400'\n                      : 'text-gray-300 hover:text-white'\n                  }`}\n                >\n                  {item.name}\n                  {pathname === item.href && (\n                    <motion.div\n                      className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-teal-500\"\n                      layoutId=\"activeTab\"\n                      initial={false}\n                      transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                    />\n                  )}\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Desktop CTA Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n            >\n              <Button variant=\"ghost\" size=\"sm\">\n                Sign In\n              </Button>\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: 0.5 }}\n            >\n              <Button variant=\"primary\" size=\"sm\">\n                Get Started\n              </Button>\n            </motion.div>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <div className=\"lg:hidden\">\n            <motion.button\n              className=\"p-2 text-gray-300 hover:text-white transition-colors\"\n              onClick={() => setIsOpen(!isOpen)}\n              whileTap={{ scale: 0.95 }}\n            >\n              <AnimatePresence mode=\"wait\">\n                {isOpen ? (\n                  <motion.div\n                    key=\"close\"\n                    initial={{ rotate: -90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: 90, opacity: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <X size={24} />\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"menu\"\n                    initial={{ rotate: 90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: -90, opacity: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <Menu size={24} />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"lg:hidden overflow-hidden\"\n              variants={mobileMenuVariants}\n              initial=\"closed\"\n              animate=\"open\"\n              exit=\"closed\"\n            >\n              <div className=\"px-2 pt-2 pb-6 space-y-1 glass rounded-lg mt-2 border border-white/10\">\n                {navItems.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 * index }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={`block px-3 py-2 text-base font-medium rounded-md transition-colors ${\n                        pathname === item.href\n                          ? 'text-blue-400 bg-blue-500/10'\n                          : 'text-gray-300 hover:text-white hover:bg-white/5'\n                      }`}\n                      onClick={() => setIsOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                \n                <div className=\"pt-4 space-y-2\">\n                  <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">\n                    Sign In\n                  </Button>\n                  <Button variant=\"primary\" size=\"sm\" className=\"w-full\">\n                    Get Started\n                  </Button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAa;QACvC;YAAE,MAAM;YAAW,MAAM;QAAY;QACrC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,MAAM,cAAc;QAClB,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,MAAM,qBAAqB;QACzB,QAAQ;YACN,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,OAAO;YACL,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,4DAA4D,EACtE,WACI,oDACA,kBACJ;QACF,UAAU;QACV,SAAQ;QACR,SAAQ;kBAER,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC,+HAAA,CAAA,UAAI;oCAAC,MAAK;oCAAK,UAAU;;;;;;;;;;;;;;;;sCAK9B,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,YAAY;wCAAE,OAAO,MAAM;oCAAM;8CAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,sEAAsE,EAChF,aAAa,KAAK,IAAI,GAClB,kBACA,kCACJ;;4CAED,KAAK,IAAI;4CACT,aAAa,KAAK,IAAI,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,UAAS;gDACT,SAAS;gDACT,YAAY;oDAAE,MAAM;oDAAU,WAAW;oDAAK,SAAS;gDAAG;;;;;;;;;;;;mCArB3D,KAAK,IAAI;;;;;;;;;;sCA8BpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;;;;;;8CAIpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;sCAOxC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,IAAM,UAAU,CAAC;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oCAAC,MAAK;8CACnB,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,QAAQ,CAAC;4CAAI,SAAS;wCAAE;wCACnC,SAAS;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCACjC,MAAM;4CAAE,QAAQ;4CAAI,SAAS;wCAAE;wCAC/B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;uCANL;;;;6DASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,QAAQ;4CAAI,SAAS;wCAAE;wCAClC,SAAS;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCACjC,MAAM;4CAAE,QAAQ,CAAC;4CAAI,SAAS;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;uCANR;;;;;;;;;;;;;;;;;;;;;;;;;;8BAehB,8OAAC,yLAAA,CAAA,kBAAe;8BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;kCAEL,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM;wCAAM;kDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,iCACA,mDACJ;4CACF,SAAS,IAAM,UAAU;sDAExB,KAAK,IAAI;;;;;;uCAdP,KAAK,IAAI;;;;;8CAmBlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAAS;;;;;;sDAGrD,8OAAC,iIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzE", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/ui/ParticleBackground.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport * as THREE from 'three';\n\nexport default function ParticleBackground({ \n  particleCount = 1000,\n  particleSize = 2,\n  animationSpeed = 0.001,\n  mouseInteraction = true,\n  colors = ['#0ea5e9', '#14b8a6', '#8b5cf6', '#ec4899']\n}) {\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const particlesRef = useRef(null);\n  const mouseRef = useRef({ x: 0, y: 0 });\n  const frameRef = useRef(null);\n\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    const camera = new THREE.PerspectiveCamera(\n      75,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      1000\n    );\n    const renderer = new THREE.WebGLRenderer({ \n      alpha: true, \n      antialias: true \n    });\n\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n    mountRef.current.appendChild(renderer.domElement);\n\n    // Store references\n    sceneRef.current = scene;\n    rendererRef.current = renderer;\n\n    // Create particles\n    const geometry = new THREE.BufferGeometry();\n    const positions = new Float32Array(particleCount * 3);\n    const velocities = new Float32Array(particleCount * 3);\n    const particleColors = new Float32Array(particleCount * 3);\n\n    for (let i = 0; i < particleCount; i++) {\n      const i3 = i * 3;\n      \n      // Random positions\n      positions[i3] = (Math.random() - 0.5) * 100;\n      positions[i3 + 1] = (Math.random() - 0.5) * 100;\n      positions[i3 + 2] = (Math.random() - 0.5) * 100;\n\n      // Random velocities\n      velocities[i3] = (Math.random() - 0.5) * 0.02;\n      velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;\n      velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;\n\n      // Random colors from palette\n      const color = new THREE.Color(colors[Math.floor(Math.random() * colors.length)]);\n      particleColors[i3] = color.r;\n      particleColors[i3 + 1] = color.g;\n      particleColors[i3 + 2] = color.b;\n    }\n\n    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));\n    geometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));\n\n    // Particle material\n    const material = new THREE.PointsMaterial({\n      size: particleSize,\n      vertexColors: true,\n      transparent: true,\n      opacity: 0.8,\n      blending: THREE.AdditiveBlending,\n    });\n\n    // Create particle system\n    const particles = new THREE.Points(geometry, material);\n    scene.add(particles);\n    particlesRef.current = { particles, velocities, positions };\n\n    // Camera position\n    camera.position.z = 50;\n\n    // Mouse interaction\n    const handleMouseMove = (event) => {\n      if (!mouseInteraction) return;\n      \n      mouseRef.current.x = (event.clientX / window.innerWidth) * 2 - 1;\n      mouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1;\n    };\n\n    // Resize handler\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n\n    // Animation loop\n    const animate = () => {\n      frameRef.current = requestAnimationFrame(animate);\n\n      if (particlesRef.current) {\n        const { particles, velocities, positions } = particlesRef.current;\n        const positionAttribute = particles.geometry.attributes.position;\n\n        // Update particle positions\n        for (let i = 0; i < particleCount; i++) {\n          const i3 = i * 3;\n\n          // Apply velocities\n          positions[i3] += velocities[i3];\n          positions[i3 + 1] += velocities[i3 + 1];\n          positions[i3 + 2] += velocities[i3 + 2];\n\n          // Mouse interaction\n          if (mouseInteraction) {\n            const mouseInfluence = 0.1;\n            const dx = mouseRef.current.x * 50 - positions[i3];\n            const dy = mouseRef.current.y * 50 - positions[i3 + 1];\n            \n            velocities[i3] += dx * mouseInfluence * animationSpeed;\n            velocities[i3 + 1] += dy * mouseInfluence * animationSpeed;\n          }\n\n          // Boundary wrapping\n          if (positions[i3] > 50) positions[i3] = -50;\n          if (positions[i3] < -50) positions[i3] = 50;\n          if (positions[i3 + 1] > 50) positions[i3 + 1] = -50;\n          if (positions[i3 + 1] < -50) positions[i3 + 1] = 50;\n          if (positions[i3 + 2] > 50) positions[i3 + 2] = -50;\n          if (positions[i3 + 2] < -50) positions[i3 + 2] = 50;\n\n          // Apply damping\n          velocities[i3] *= 0.99;\n          velocities[i3 + 1] *= 0.99;\n          velocities[i3 + 2] *= 0.99;\n        }\n\n        positionAttribute.needsUpdate = true;\n\n        // Rotate particle system\n        particles.rotation.x += animationSpeed;\n        particles.rotation.y += animationSpeed * 0.5;\n      }\n\n      renderer.render(scene, camera);\n    };\n\n    // Event listeners\n    window.addEventListener('mousemove', handleMouseMove);\n    window.addEventListener('resize', handleResize);\n\n    // Start animation\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('mousemove', handleMouseMove);\n      window.removeEventListener('resize', handleResize);\n      \n      if (frameRef.current) {\n        cancelAnimationFrame(frameRef.current);\n      }\n      \n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      \n      // Dispose of Three.js objects\n      if (particlesRef.current) {\n        particlesRef.current.particles.geometry.dispose();\n        particlesRef.current.particles.material.dispose();\n      }\n      renderer.dispose();\n    };\n  }, [particleCount, particleSize, animationSpeed, mouseInteraction, colors]);\n\n  return (\n    <div \n      ref={mountRef} \n      className=\"fixed inset-0 pointer-events-none z-0\"\n      style={{ \n        background: 'transparent',\n        overflow: 'hidden'\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS,mBAAmB,EACzC,gBAAgB,IAAI,EACpB,eAAe,CAAC,EAChB,iBAAiB,KAAK,EACtB,mBAAmB,IAAI,EACvB,SAAS;IAAC;IAAW;IAAW;IAAW;CAAU,EACtD;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,cAAc;QACd,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAW;QAC7B,MAAM,SAAS,IAAI,+IAAA,CAAA,oBAAuB,CACxC,IACA,OAAO,UAAU,GAAG,OAAO,WAAW,EACtC,KACA;QAEF,MAAM,WAAW,IAAI,iKAAA,CAAA,gBAAmB,CAAC;YACvC,OAAO;YACP,WAAW;QACb;QAEA,SAAS,OAAO,CAAC,OAAO,UAAU,EAAE,OAAO,WAAW;QACtD,SAAS,aAAa,CAAC,KAAK,GAAG,CAAC,OAAO,gBAAgB,EAAE;QACzD,SAAS,OAAO,CAAC,WAAW,CAAC,SAAS,UAAU;QAEhD,mBAAmB;QACnB,SAAS,OAAO,GAAG;QACnB,YAAY,OAAO,GAAG;QAEtB,mBAAmB;QACnB,MAAM,WAAW,IAAI,+IAAA,CAAA,iBAAoB;QACzC,MAAM,YAAY,IAAI,aAAa,gBAAgB;QACnD,MAAM,aAAa,IAAI,aAAa,gBAAgB;QACpD,MAAM,iBAAiB,IAAI,aAAa,gBAAgB;QAExD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,MAAM,KAAK,IAAI;YAEf,mBAAmB;YACnB,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC5C,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE5C,oBAAoB;YACpB,UAAU,CAAC,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC7C,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAE7C,6BAA6B;YAC7B,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAW,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;YAC/E,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC;YAC5B,cAAc,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC;YAChC,cAAc,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC;QAClC;QAEA,SAAS,YAAY,CAAC,YAAY,IAAI,+IAAA,CAAA,kBAAqB,CAAC,WAAW;QACvE,SAAS,YAAY,CAAC,SAAS,IAAI,+IAAA,CAAA,kBAAqB,CAAC,gBAAgB;QAEzE,oBAAoB;QACpB,MAAM,WAAW,IAAI,+IAAA,CAAA,iBAAoB,CAAC;YACxC,MAAM;YACN,cAAc;YACd,aAAa;YACb,SAAS;YACT,UAAU,+IAAA,CAAA,mBAAsB;QAClC;QAEA,yBAAyB;QACzB,MAAM,YAAY,IAAI,+IAAA,CAAA,SAAY,CAAC,UAAU;QAC7C,MAAM,GAAG,CAAC;QACV,aAAa,OAAO,GAAG;YAAE;YAAW;YAAY;QAAU;QAE1D,kBAAkB;QAClB,OAAO,QAAQ,CAAC,CAAC,GAAG;QAEpB,oBAAoB;QACpB,MAAM,kBAAkB,CAAC;YACvB,IAAI,CAAC,kBAAkB;YAEvB,SAAS,OAAO,CAAC,CAAC,GAAG,AAAC,MAAM,OAAO,GAAG,OAAO,UAAU,GAAI,IAAI;YAC/D,SAAS,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,OAAO,GAAG,OAAO,WAAW,IAAI,IAAI;QACnE;QAEA,iBAAiB;QACjB,MAAM,eAAe;YACnB,OAAO,MAAM,GAAG,OAAO,UAAU,GAAG,OAAO,WAAW;YACtD,OAAO,sBAAsB;YAC7B,SAAS,OAAO,CAAC,OAAO,UAAU,EAAE,OAAO,WAAW;QACxD;QAEA,iBAAiB;QACjB,MAAM,UAAU;YACd,SAAS,OAAO,GAAG,sBAAsB;YAEzC,IAAI,aAAa,OAAO,EAAE;gBACxB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,aAAa,OAAO;gBACjE,MAAM,oBAAoB,UAAU,QAAQ,CAAC,UAAU,CAAC,QAAQ;gBAEhE,4BAA4B;gBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;oBACtC,MAAM,KAAK,IAAI;oBAEf,mBAAmB;oBACnB,SAAS,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG;oBAC/B,SAAS,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE;oBACvC,SAAS,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE;oBAEvC,oBAAoB;oBACpB,IAAI,kBAAkB;wBACpB,MAAM,iBAAiB;wBACvB,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG;wBAClD,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,KAAK,EAAE;wBAEtD,UAAU,CAAC,GAAG,IAAI,KAAK,iBAAiB;wBACxC,UAAU,CAAC,KAAK,EAAE,IAAI,KAAK,iBAAiB;oBAC9C;oBAEA,oBAAoB;oBACpB,IAAI,SAAS,CAAC,GAAG,GAAG,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC;oBACzC,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG;oBACzC,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;oBACjD,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG;oBACjD,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;oBACjD,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG;oBAEjD,gBAAgB;oBAChB,UAAU,CAAC,GAAG,IAAI;oBAClB,UAAU,CAAC,KAAK,EAAE,IAAI;oBACtB,UAAU,CAAC,KAAK,EAAE,IAAI;gBACxB;gBAEA,kBAAkB,WAAW,GAAG;gBAEhC,yBAAyB;gBACzB,UAAU,QAAQ,CAAC,CAAC,IAAI;gBACxB,UAAU,QAAQ,CAAC,CAAC,IAAI,iBAAiB;YAC3C;YAEA,SAAS,MAAM,CAAC,OAAO;QACzB;QAEA,kBAAkB;QAClB,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,gBAAgB,CAAC,UAAU;QAElC,kBAAkB;QAClB;QAEA,UAAU;QACV,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,OAAO,mBAAmB,CAAC,UAAU;YAErC,IAAI,SAAS,OAAO,EAAE;gBACpB,qBAAqB,SAAS,OAAO;YACvC;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,UAAU,EAAE;gBAC3C,SAAS,OAAO,CAAC,WAAW,CAAC,SAAS,UAAU;YAClD;YAEA,8BAA8B;YAC9B,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO;gBAC/C,aAAa,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO;YACjD;YACA,SAAS,OAAO;QAClB;IACF,GAAG;QAAC;QAAe;QAAc;QAAgB;QAAkB;KAAO;IAE1E,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,YAAY;YACZ,UAAU;QACZ;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Logo from '../ui/Logo';\nimport Button from '../ui/Button';\nimport { Play, ArrowRight, MapPin, Shield, Zap } from 'lucide-react';\n\nexport default function HeroSection() {\n  const [currentText, setCurrentText] = useState(0);\n  const [isTyping, setIsTyping] = useState(true);\n\n  const heroTexts = [\n    \"Report. Route. Resolve.\",\n    \"Cross-Border. Seamless. Secure.\",\n    \"Malaysia. Singapore. United.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setIsTyping(false);\n      setTimeout(() => {\n        setCurrentText((prev) => (prev + 1) % heroTexts.length);\n        setIsTyping(true);\n      }, 500);\n    }, 4000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const containerVariants = {\n    initial: { opacity: 0 },\n    animate: {\n      opacity: 1,\n      transition: {\n        duration: 1,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    initial: { opacity: 0, y: 30 },\n    animate: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const floatingVariants = {\n    initial: { y: 0 },\n    animate: {\n      y: [-10, 10, -10],\n      transition: {\n        duration: 6,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  const statsData = [\n    { value: \"2\", label: \"Countries\", icon: MapPin },\n    { value: \"99.9%\", label: \"Uptime\", icon: Shield },\n    { value: \"<2min\", label: \"Response\", icon: Zap }\n  ];\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/30 to-slate-900/50\" />\n      \n      {/* Animated Background Shapes */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl\"\n        variants={floatingVariants}\n        initial=\"initial\"\n        animate=\"animate\"\n      />\n      <motion.div\n        className=\"absolute bottom-20 right-10 w-48 h-48 bg-teal-500/10 rounded-full blur-xl\"\n        variants={floatingVariants}\n        initial=\"initial\"\n        animate=\"animate\"\n        transition={{ delay: 1 }}\n      />\n      <motion.div\n        className=\"absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/10 rounded-full blur-xl\"\n        variants={floatingVariants}\n        initial=\"initial\"\n        animate=\"animate\"\n        transition={{ delay: 2 }}\n      />\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"initial\"\n          animate=\"animate\"\n          className=\"space-y-8\"\n        >\n          {/* Logo */}\n          <motion.div variants={itemVariants} className=\"flex justify-center\">\n            <Logo size=\"xl\" animated={true} />\n          </motion.div>\n\n          {/* Main Headline */}\n          <motion.div variants={itemVariants} className=\"space-y-4\">\n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold\">\n              <span className=\"bg-gradient-to-r from-blue-400 via-teal-400 to-purple-400 bg-clip-text text-transparent\">\n                {isTyping ? (\n                  <motion.span\n                    key={currentText}\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"typewriter\"\n                  >\n                    {heroTexts[currentText]}\n                  </motion.span>\n                ) : (\n                  <span className=\"opacity-50\">{heroTexts[currentText]}</span>\n                )}\n              </span>\n            </h1>\n            \n            <p className=\"text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n              Simplify offense reporting across{' '}\n              <span className=\"text-teal-400 font-semibold\">Malaysia</span> and{' '}\n              <span className=\"text-purple-400 font-semibold\">Singapore</span> with \n              smart routing, real-time tracking, and multimedia evidence submission.\n            </p>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Link href=\"/demo\">\n              <Button \n                variant=\"primary\" \n                size=\"lg\"\n                icon={<ArrowRight size={20} />}\n                iconPosition=\"right\"\n                className=\"w-full sm:w-auto\"\n              >\n                Start Reporting Now\n              </Button>\n            </Link>\n            \n            <Button \n              variant=\"secondary\" \n              size=\"lg\"\n              icon={<Play size={20} />}\n              className=\"w-full sm:w-auto\"\n            >\n              Watch Demo\n            </Button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div \n            variants={itemVariants}\n            className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto mt-16\"\n          >\n            {statsData.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                className=\"glass-card text-center p-6\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"flex justify-center mb-3\">\n                  <stat.icon className=\"w-8 h-8 text-blue-400\" />\n                </div>\n                <div className=\"text-2xl font-bold text-white mb-1\">\n                  {stat.value}\n                </div>\n                <div className=\"text-gray-400 text-sm\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-wrap justify-center items-center gap-8 mt-12 opacity-60\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <Shield className=\"w-5 h-5 text-green-400\" />\n              <span className=\"text-sm text-gray-400\">Government Certified</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <MapPin className=\"w-5 h-5 text-blue-400\" />\n              <span className=\"text-sm text-gray-400\">Cross-Border Ready</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Zap className=\"w-5 h-5 text-yellow-400\" />\n              <span className=\"text-sm text-gray-400\">Real-Time Processing</span>\n            </div>\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 2, duration: 1 }}\n          >\n            <motion.div\n              className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\"\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              <motion.div\n                className=\"w-1 h-3 bg-white/50 rounded-full mt-2\"\n                animate={{ opacity: [1, 0, 1] }}\n                transition={{ duration: 2, repeat: Infinity }}\n              />\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,YAAY;QAChB;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,YAAY;YACZ,WAAW;gBACT,eAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;gBACtD,YAAY;YACd,GAAG;QACL,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,SAAS;YAAE,GAAG;QAAE;QAChB,SAAS;YACP,GAAG;gBAAC,CAAC;gBAAI;gBAAI,CAAC;aAAG;YACjB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,MAAM,YAAY;QAChB;YAAE,OAAO;YAAK,OAAO;YAAa,MAAM,0MAAA,CAAA,SAAM;QAAC;QAC/C;YAAE,OAAO;YAAS,OAAO;YAAU,MAAM,sMAAA,CAAA,SAAM;QAAC;QAChD;YAAE,OAAO;YAAS,OAAO;YAAY,MAAM,gMAAA,CAAA,MAAG;QAAC;KAChD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;;;;;0BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,YAAY;oBAAE,OAAO;gBAAE;;;;;;0BAEzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,YAAY;oBAAE,OAAO;gBAAE;;;;;;0BAGzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,8OAAC,+HAAA,CAAA,UAAI;gCAAC,MAAK;gCAAK,UAAU;;;;;;;;;;;sCAI5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAK,WAAU;kDACb,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CAEV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,MAAM;gDAAE,SAAS;4CAAE;4CACnB,WAAU;sDAET,SAAS,CAAC,YAAY;2CANlB;;;;iEASP,8OAAC;4CAAK,WAAU;sDAAc,SAAS,CAAC,YAAY;;;;;;;;;;;;;;;;8CAK1D,8OAAC;oCAAE,WAAU;;wCAAsE;wCAC/C;sDAClC,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAe;wCAAK;sDAClE,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;wCAAgB;;;;;;;;;;;;;sCAMpE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,iIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,oBAAM,8OAAC,kNAAA,CAAA,aAAU;4CAAC,MAAM;;;;;;wCACxB,cAAa;wCACb,WAAU;kDACX;;;;;;;;;;;8CAKH,8OAAC,iIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAZR,KAAK,KAAK;;;;;;;;;;sCAmBrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAG,UAAU;4BAAE;sCAEpC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;0CAE5C,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;oCAAC;oCAC9B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D", "debugId": null}}]}